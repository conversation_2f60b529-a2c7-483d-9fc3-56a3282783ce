/** @type {import('next').NextConfig} */
const nextConfig = {
  // 性能优化: 仅在生产模式启用 optimizePackageImports
  experimental: process.env.NODE_ENV === 'production' ? {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-dropdown-menu'],
  } : {},

  // 编译优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // 图片配置
  images: {
    unoptimized: true,
    domains: [
      "source.unsplash.com",
      "images.unsplash.com",
      "ext.same-assets.com",
      "ugc.same-assets.com",
    ],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "source.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ext.same-assets.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ugc.same-assets.com",
        pathname: "/**",
      },
    ],
  },

  // Webpack配置优化
  webpack: (config, { isServer, dev }) => {
    // 开发环境优化
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };

      // 简化代码分割策略，避免复杂的模块依赖问题
      if (!isServer) {
        // 减少代码分割，避免模块加载错误
        config.optimization = {
          ...config.optimization,
          splitChunks: {
            chunks: 'async', // 只对异步加载的chunks进行分割
            minSize: 30000,
            maxSize: 0,
            cacheGroups: {
              default: {
                minChunks: 2,
                priority: -20,
                reuseExistingChunk: true,
              },
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendors',
                priority: -10,
                chunks: 'all',
              },
            },
          },
        };

        // 添加模块解析优化
        config.resolve = {
          ...config.resolve,
          symlinks: false, // 禁用符号链接解析，提升性能
        };
      }
    }

    // 处理@napi-rs/canvas原生模块
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        '@napi-rs/canvas': 'commonjs @napi-rs/canvas',
        'canvas': 'commonjs canvas'
      });
    }

    // 忽略原生模块的webpack处理
    config.module.rules.push({
      test: /\.node$/,
      use: 'ignore-loader'
    });

    return config;
  },
};

module.exports = nextConfig;
