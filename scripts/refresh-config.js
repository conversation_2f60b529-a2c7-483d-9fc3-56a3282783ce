#!/usr/bin/env node

/**
 * 配置刷新脚本
 * 用于在修改数据库配置后立即刷新缓存
 */

const https = require('https');
const http = require('http');

// 配置
const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
const REFRESH_ENDPOINT = '/api/admin/refresh-config';

/**
 * 发送HTTP请求
 */
function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ status: res.statusCode, data: result });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

/**
 * 刷新配置
 */
async function refreshConfig() {
  console.log('🔄 正在刷新配置缓存...');
  console.log(`📡 API地址: ${API_BASE}${REFRESH_ENDPOINT}`);
  
  try {
    // 1. 检查当前状态
    console.log('\n📊 检查当前配置状态...');
    const statusResponse = await makeRequest(`${API_BASE}${REFRESH_ENDPOINT}`, 'GET');
    
    if (statusResponse.status === 200) {
      console.log('✅ 当前状态:', JSON.stringify(statusResponse.data, null, 2));
    } else {
      console.log('⚠️ 状态检查失败:', statusResponse.status, statusResponse.data);
    }
    
    // 2. 刷新缓存
    console.log('\n🔄 执行缓存刷新...');
    const refreshResponse = await makeRequest(`${API_BASE}${REFRESH_ENDPOINT}`, 'POST');
    
    if (refreshResponse.status === 200) {
      console.log('✅ 缓存刷新成功!');
      console.log('📊 刷新结果:', JSON.stringify(refreshResponse.data, null, 2));
    } else {
      console.log('❌ 缓存刷新失败:', refreshResponse.status, refreshResponse.data);
      process.exit(1);
    }
    
    // 3. 验证刷新结果
    console.log('\n🔍 验证刷新结果...');
    const verifyResponse = await makeRequest(`${API_BASE}${REFRESH_ENDPOINT}`, 'GET');
    
    if (verifyResponse.status === 200) {
      console.log('✅ 验证成功:', JSON.stringify(verifyResponse.data, null, 2));
    } else {
      console.log('⚠️ 验证失败:', verifyResponse.status, verifyResponse.data);
    }
    
    console.log('\n🎉 配置刷新完成!');
    console.log('💡 提示: 如果前端页面仍显示旧配置，请刷新浏览器页面');
    
  } catch (error) {
    console.error('❌ 配置刷新失败:', error.message);
    console.log('\n🔧 故障排除建议:');
    console.log('1. 检查开发服务器是否正在运行');
    console.log('2. 检查API地址是否正确');
    console.log('3. 检查网络连接');
    process.exit(1);
  }
}

// 主函数
async function main() {
  console.log('🚀 配置刷新工具');
  console.log('================');
  
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('用法:');
    console.log('  node scripts/refresh-config.js          # 刷新配置');
    console.log('  node scripts/refresh-config.js --help   # 显示帮助');
    console.log('');
    console.log('环境变量:');
    console.log('  NEXT_PUBLIC_API_URL  # API基础地址 (默认: http://localhost:3001)');
    return;
  }
  
  await refreshConfig();
}

// 运行
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { refreshConfig };
