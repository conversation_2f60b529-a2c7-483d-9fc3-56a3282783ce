import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

// 数据库配置缓存
let databaseConfigsCache: Record<string, any> | null = null;
let configsCacheExpiry: number = 0;
const CACHE_TTL = 10 * 60 * 1000; // 10分钟缓存

// 获取数据库图标 - 移除硬编码，使用通用图标
function getDatabaseIcon(code: string): string {
  // 根据数据库代码的模式返回合适的图标
  if (code.startsWith('device')) return '🏥';
  if (code.startsWith('us_')) return '🇺🇸';
  if (code.startsWith('subject')) return '💊';
  if (code.includes('patent') || code.includes('Pat')) return '📋';

  return '📊'; // 默认图标
}

export async function GET() {
  try {
    const now = Date.now();

    // 如果缓存有效，直接返回
    if (databaseConfigsCache && now < configsCacheExpiry) {
      return NextResponse.json({
        success: true,
        data: databaseConfigsCache
      });
    }

    // 从数据库获取配置
    const configs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        description: true,
        accessLevel: true,
        defaultSort: true,
        sortOrder: true
      },
      orderBy: { sortOrder: 'asc' }
    });

    // 构建配置对象
    const configsMap: Record<string, any> = {};
    configs.forEach(config => {
      configsMap[config.code] = {
        name: config.name,
        category: config.category,
        description: config.description || '',
        accessLevel: config.accessLevel,
        defaultSort: config.defaultSort || null,
        sortOrder: config.sortOrder,
        icon: getDatabaseIcon(config.code),
      };
    });

    // 设置缓存
    databaseConfigsCache = configsMap;
    configsCacheExpiry = now + CACHE_TTL;

    return NextResponse.json({
      success: true,
      data: configsMap
    });

  } catch (error) {
    console.error('获取数据库配置失败:', error);
    
    // 回退到硬编码配置 - 只包含实际存在的数据库
    const fallbackConfigs = {
      us_class: {
        name: 'US Classification',
        category: 'Regular',
        description: 'US Medical Device Classification Database',
        accessLevel: 'free',
        icon: '🇺🇸',
      },
      us_pmn: {
        name: 'US Premarket Notification',
        category: 'Marketed',
        description: 'US FDA Premarket Notification (510k) Database',
        accessLevel: 'free',
        icon: '🇺🇸',
      },
    };

    return NextResponse.json({
      success: true,
      data: fallbackConfigs
    });
  }
}
