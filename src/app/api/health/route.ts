import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

export async function GET() {
  const checks = {
    database: false,
    session: false,
    environment: false,
    analytics: false,
  };

  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // 1. 检查数据库连接
    try {
      await db.$queryRaw`SELECT 1`;
      checks.database = true;
    } catch (error) {
      errors.push('数据库连接失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }

    // 2. 检查会话密钥
    if (process.env.SESSION_SECRET) {
      if (process.env.SESSION_SECRET.length >= 32) {
        checks.session = true;
      } else {
        warnings.push('SESSION_SECRET 长度应至少为32个字符');
      }
    } else {
      errors.push('SESSION_SECRET 环境变量未设置');
    }

    // 3. 检查必要的环境变量
    const requiredEnvVars = ['DATABASE_URL', 'SESSION_SECRET'];
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingEnvVars.length === 0) {
      checks.environment = true;
    } else {
      errors.push(`缺少环境变量: ${missingEnvVars.join(', ')}`);
    }

    // 4. 检查分析功能
    try {
      const recentLogs = await db.activityLog.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // 最近24小时
          },
        },
      });
      checks.analytics = true;
    } catch (error) {
      warnings.push('分析功能检查失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }

    // 5. 检查数据库表
    try {
      const tables = await db.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      ` as Array<{ table_name: string }>;
      
      const requiredTables = ['User', 'ActivityLog', 'DatabaseConfig'];
      const existingTables = tables.map(t => t.table_name);
      const missingTables = requiredTables.filter(table => !existingTables.includes(table));
      
      if (missingTables.length > 0) {
        warnings.push(`缺少数据库表: ${missingTables.join(', ')}`);
      }
    } catch (error) {
      warnings.push('数据库表检查失败');
    }

    const allChecksPass = Object.values(checks).every(check => check);
    const status = errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'healthy';

    return NextResponse.json({
      status,
      healthy: allChecksPass && errors.length === 0,
      checks,
      errors,
      warnings,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      recommendations: [
        ...(errors.length > 0 ? ['请修复所有错误后重新检查'] : []),
        ...(warnings.length > 0 ? ['建议处理所有警告以确保最佳性能'] : []),
        ...(status === 'healthy' ? ['系统运行正常'] : []),
      ],
    });

  } catch (error) {
    return NextResponse.json({
      status: 'error',
      healthy: false,
      checks,
      errors: ['健康检查失败: ' + (error instanceof Error ? error.message : '未知错误')],
      warnings,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
