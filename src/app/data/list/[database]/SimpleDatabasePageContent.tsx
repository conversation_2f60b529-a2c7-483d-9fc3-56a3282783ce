"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ChevronLeft,
  ChevronRight,
  Filter,
  Download,
  ArrowUpDown,
  ChevronUp,
  ChevronDown,
  X,
  Loader2,
  BarChart3,
  AlertCircle,
  Database
} from "lucide-react";
import Link from "next/link";

// 内联类型定义，避免外部依赖
interface DataRecord extends Record<string, unknown> {}

interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    maxPages: number;
    isAtMaxPages: boolean;
    maxPageSize: number;
    defaultPageSize: number;
  };
  filters?: Record<string, unknown>;
  config?: any;
  error?: string;
}

interface FieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: string;
  isVisible: boolean;
  isFilterable: boolean;
  isSortable: boolean;
  listOrder: number;
  filterType?: string;
  todetail?: boolean;
}

interface DatabaseConfig {
  name: string;
  accessLevel: string;
  fields: FieldConfig[];
  defaultSort?: Array<{ field: string; order: 'asc' | 'desc' }>;
}

interface SimpleDatabasePageContentProps {
  database: string;
}

// 内联工具函数
const formatDate = (value: any): string => {
  if (!value) return '';
  const str = String(value);
  if (str.length === 10 && /^\d{4}-\d{2}-\d{2}$/.test(str)) {
    return str;
  }
  if (str.length > 10) {
    return str.substring(0, 10);
  }
  return str;
};

export default function SimpleDatabasePageContent({ database }: SimpleDatabasePageContentProps) {
  const searchParams = useSearchParams();

  // 状态管理
  const [data, setData] = useState<DataRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<DatabaseConfig | null>(null);
  const [tableHeaders, setTableHeaders] = useState<FieldConfig[]>([]);
  
  // 权限检查
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [accessCheckLoading, setAccessCheckLoading] = useState(true);
  
  // 筛选和分页
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [metadata, setMetadata] = useState<Record<string, string[]>>({});
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalCount: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
    maxPages: 100,
    isAtMaxPages: false,
    maxPageSize: 100,
    defaultPageSize: 20,
  });
  
  // 排序
  const [sortBy, setSortBy] = useState<string>('id');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // UI 状态
  const [filterOpen, setFilterOpen] = useState(true);

  // 简化的权限检查
  useEffect(() => {
    const checkAccess = async () => {
      try {
        const response = await fetch('/api/config/databases');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data[database]) {
            const dbConfig = result.data[database];
            // 简化权限检查：只检查是否为 free 级别
            setHasAccess(dbConfig.accessLevel === 'free');
          } else {
            setHasAccess(false);
          }
        } else {
          setHasAccess(false);
        }
      } catch (error) {
        console.error('权限检查失败:', error);
        setHasAccess(false);
      } finally {
        setAccessCheckLoading(false);
      }
    };

    checkAccess();
  }, [database]);

  // 获取配置和元数据
  useEffect(() => {
    if (!hasAccess) return;

    const fetchConfigAndMeta = async () => {
      try {
        // 获取元数据
        const metaResponse = await fetch(`/api/meta/${database}`);
        const metaResult = await metaResponse.json();
        if (metaResult.success) {
          setMetadata(metaResult.data || {});
          if (metaResult.config) {
            setConfig(metaResult.config);
            if (metaResult.config.fields) {
              const visibleFields = metaResult.config.fields
                .filter((f: FieldConfig) => f.isVisible)
                .sort((a: FieldConfig, b: FieldConfig) => a.listOrder - b.listOrder);
              setTableHeaders(visibleFields);
            }
          }
        }
      } catch (error) {
        console.error('获取配置失败:', error);
      }
    };

    fetchConfigAndMeta();
  }, [database, hasAccess]);

  // 从URL参数初始化筛选条件
  useEffect(() => {
    const newFilters: Record<string, unknown> = {};
    searchParams.forEach((value, key) => {
      newFilters[key] = value;
    });
    setFilters(newFilters);
  }, [searchParams]);

  // 加载数据
  const loadData = useCallback(async (
    page: number,
    currentFilters: Record<string, unknown>,
    currentSortBy?: string,
    currentSortOrder?: 'asc' | 'desc'
  ) => {
    if (!hasAccess) return;

    try {
      setLoading(true);
      setError(null);

      const searchParams = {
        page: String(page),
        limit: String(pagination.limit),
        sortBy: currentSortBy || sortBy,
        sortOrder: currentSortOrder || sortOrder,
        filters: JSON.stringify(currentFilters),
      };

      const queryString = new URLSearchParams(searchParams).toString();
      const response = await fetch(`/api/data/${database}?${queryString}`);
      
      if (!response.ok) {
        throw new Error('加载数据失败');
      }

      const result = await response.json();
      if (result.success && result.data) {
        setData(result.data);
        setPagination(prev => ({
          ...prev,
          page: result.pagination.page,
          totalCount: result.pagination.totalCount,
          totalPages: result.pagination.totalPages,
          hasNext: result.pagination.hasNext,
          hasPrev: result.pagination.hasPrev,
          isAtMaxPages: result.pagination.isAtMaxPages,
          maxPages: result.pagination.maxPages,
          maxPageSize: result.pagination.maxPageSize,
          defaultPageSize: result.pagination.defaultPageSize,
        }));
      } else {
        setError(result.error || '加载数据失败');
      }
    } catch (err) {
      console.error('加载数据失败:', err);
      setError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [database, hasAccess, pagination.limit, sortBy, sortOrder]);

  // 初始数据加载
  useEffect(() => {
    if (hasAccess && config) {
      loadData(1, filters);
    }
  }, [loadData, hasAccess, config, filters]);

  // 处理筛选器变更
  const handleFilterChange = (key: string, value: unknown) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 应用筛选器
  const handleSearch = () => {
    loadData(1, filters);
  };

  // 重置筛选器
  const resetFilters = () => {
    setFilters({});
    loadData(1, {});
  };

  // 处理分页
  const handlePageChange = (direction: 'prev' | 'next') => {
    const newPage = direction === 'prev' ? pagination.page - 1 : pagination.page + 1;
    if (newPage >= 1 && newPage <= pagination.totalPages && newPage <= pagination.maxPages) {
      loadData(newPage, filters);
    }
  };

  // 处理排序
  const handleSort = (fieldName: string) => {
    const field = tableHeaders.find(f => f.fieldName === fieldName);
    if (!field?.isSortable) return;

    let newSortOrder: 'asc' | 'desc' = 'desc';
    if (sortBy === fieldName) {
      newSortOrder = sortOrder === 'desc' ? 'asc' : 'desc';
    }

    setSortBy(fieldName);
    setSortOrder(newSortOrder);
    loadData(1, filters, fieldName, newSortOrder);
  };

  // 权限检查中
  if (accessCheckLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600 text-sm">验证权限中...</p>
        </div>
      </div>
    );
  }

  // 无权限访问
  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <span className="text-red-600 text-xl">🔒</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">访问受限</h2>
            <p className="text-gray-600 mb-4">
              该数据库需要付费权限才能访问。
            </p>
            <div className="flex flex-col gap-2">
              <a
                href="/upgrade"
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-center"
              >
                立即升级
              </a>
              <a
                href="/"
                className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 text-center"
              >
                返回首页
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const databaseName = config?.name || database;

  return (
    <div className="flex">
      {/* 筛选面板 */}
      <div className={`${filterOpen ? 'w-72' : 'w-16'} transition-all duration-300 bg-gray-50 border-r border-gray-300 flex flex-col fixed top-16 left-0 bottom-0 z-50 overflow-hidden hidden md:block`}>
        <div className="p-4 border-b border-gray-200 bg-white shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className={`font-medium text-gray-800 ${!filterOpen ? 'hidden' : ''}`}>筛选器</h3>
            <Button variant="ghost" size="sm" onClick={() => setFilterOpen(!filterOpen)} className="hover:bg-gray-100">
              {filterOpen ? <X className="h-4 w-4 text-gray-600" /> : <Filter className="h-4 w-4 text-gray-600" />}
            </Button>
          </div>
        </div>

        {filterOpen && (
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-1 overflow-y-auto p-3 space-y-3">
              {/* 动态筛选器 */}
              {tableHeaders
                .filter(field => field.isFilterable)
                .map((field) => {
                  const options = metadata[field.fieldName] || [];
                  return (
                    <div key={field.fieldName} className="space-y-1">
                      <label className="text-xs font-medium text-gray-700">
                        {field.displayName}
                      </label>
                      {field.filterType === 'select' ? (
                        <Select
                          value={(filters[field.fieldName] as string) || '__all__'}
                          onValueChange={(value) => {
                            handleFilterChange(field.fieldName, value === '__all__' ? '' : value);
                          }}
                        >
                          <SelectTrigger className="w-full h-8 text-xs">
                            <SelectValue placeholder={`选择 ${field.displayName}`} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="__all__">全部</SelectItem>
                            {options.map((option) => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Input
                          placeholder={`输入 ${field.displayName.toLowerCase()}`}
                          value={(filters[field.fieldName] as string) || ''}
                          onChange={(e) => handleFilterChange(field.fieldName, e.target.value)}
                          className="w-full text-xs h-8"
                          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        />
                      )}
                    </div>
                  );
                })}
            </div>

            {/* 操作按钮 */}
            <div className="border-t border-gray-200 p-4 bg-white flex-shrink-0">
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Button onClick={handleSearch} disabled={loading} className="flex-1">
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    应用筛选
                  </Button>
                  <Button variant="ghost" onClick={resetFilters} disabled={loading}>
                    清除
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 主要内容区域 */}
      <div className={`flex-1 ${filterOpen ? 'ml-72' : 'ml-16'} transition-all duration-300`}>
        {/* 标题栏 */}
        <div className="bg-white border-b border-gray-200 p-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">{databaseName}</h1>
            <div className="flex gap-2">
              <Button variant="outline">
                <BarChart3 className="mr-2 h-4 w-4" />
                统计
              </Button>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
            </div>
          </div>
        </div>

        {/* 数据表格 */}
        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center py-12">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 mb-2">加载失败</p>
                <p className="text-gray-500 text-sm">{error}</p>
                <Button
                  variant="outline"
                  onClick={() => loadData(pagination.page, filters)}
                  className="mt-4"
                >
                  重试
                </Button>
              </div>
            </div>
          ) : data.length === 0 ? (
            <div className="flex justify-center items-center py-12">
              <div className="text-center">
                <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">暂无数据</p>
              </div>
            </div>
          ) : (
            <>
              {/* 表头 */}
              <div className="bg-gray-50 border border-gray-200 rounded-t-lg">
                <div className="px-4 py-3 flex gap-4 text-sm font-medium text-gray-700">
                  {tableHeaders.map((header) => (
                    <div
                      key={header.fieldName}
                      className={`flex-1 min-w-0 flex items-center gap-1 ${
                        header.isSortable ? 'cursor-pointer hover:text-gray-900 select-none' : ''
                      }`}
                      onClick={() => header.isSortable && handleSort(header.fieldName)}
                    >
                      <span className="truncate">{header.displayName}</span>
                      {header.isSortable && (
                        <div className="flex flex-col">
                          {sortBy === header.fieldName ? (
                            sortOrder === 'asc' ? (
                              <ChevronUp className="h-3 w-3 text-blue-600" />
                            ) : (
                              <ChevronDown className="h-3 w-3 text-blue-600" />
                            )
                          ) : (
                            <ArrowUpDown className="h-3 w-3 text-gray-400" />
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* 表格数据 */}
              <div className="bg-white border-l border-r border-b border-gray-200 rounded-b-lg">
                {data.map((row, index) => (
                  <div
                    key={row.id ? String(row.id) : `row-${index}`}
                    className="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex gap-4 text-sm">
                      {tableHeaders.map((header) => {
                        const cellValue = header.fieldType === 'date' 
                          ? formatDate(row[header.fieldName])
                          : String(row[header.fieldName] || '');

                        return (
                          <div key={header.fieldName} className="flex-1 min-w-0">
                            {header.todetail ? (
                              <Link
                                href={`/data/detail/${database}/${String(row.id)}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer truncate block"
                                title={cellValue}
                              >
                                {cellValue}
                              </Link>
                            ) : (
                              <span className="text-gray-900 truncate block" title={cellValue}>
                                {cellValue}
                              </span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

        {/* 分页 */}
        <div className="border-t border-gray-200 p-4 bg-white">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">
              显示 {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.totalCount)} 
              共 {pagination.totalCount} 条记录
            </span>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange('prev')}
                disabled={pagination.page <= 1 || loading}
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>
              
              <span className="text-sm">第 {pagination.page} 页 共 {pagination.totalPages} 页</span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange('next')}
                disabled={pagination.page >= pagination.totalPages || pagination.isAtMaxPages || loading}
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
