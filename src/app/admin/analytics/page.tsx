"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart3, 
  Users, 
  Eye, 
  Search, 
  TrendingUp, 
  Clock,
  Database,
  MousePointer,
  RefreshCw
} from 'lucide-react';
import Navigation from '@/components/Navigation';

interface AnalyticsData {
  overview: {
    totalVisits: number;
    uniqueVisitors: number;
    pageViews: number;
    searchEvents: number;
    bounceRate: number;
    avgSessionDuration: number;
  };
  topDatabases: Array<{ name: string; visits: number }>;
  topPages: Array<{ path: string; visits: number }>;
  eventTypes: Array<{ type: string; count: number }>;
  hourlyStats: Array<{ hour: string; count: number }>;
  timeRange: string;
  generatedAt: string;
}

export default function AnalyticsPage() {
  const { user } = useAuth();
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedDatabase, setSelectedDatabase] = useState<string>('all');

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        range: timeRange,
      });
      
      if (selectedDatabase !== 'all') {
        params.append('database', selectedDatabase);
      }

      const response = await fetch(`/api/analytics/stats?${params}`);
      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || '获取分析数据失败');
      }
    } catch (err) {
      setError('网络错误，请稍后重试');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange, selectedDatabase]);

  // 检查用户权限
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <Alert>
            <AlertDescription>
              请先登录以查看分析数据。
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">网站分析</h1>
            <p className="text-gray-600 mt-2">查看网站访问统计和用户行为数据</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1d">最近1天</SelectItem>
                <SelectItem value="7d">最近7天</SelectItem>
                <SelectItem value="30d">最近30天</SelectItem>
                <SelectItem value="90d">最近90天</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedDatabase} onValueChange={setSelectedDatabase}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有数据库</SelectItem>
                <SelectItem value="us_class">美国分类</SelectItem>
                <SelectItem value="deviceUS">美国上市</SelectItem>
                <SelectItem value="freePat">医药专利</SelectItem>
                <SelectItem value="deviceCNEvaluation">中国大陆审评</SelectItem>
              </SelectContent>
            </Select>
            
            <Button onClick={fetchAnalytics} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardHeader className="pb-2">
                  <Skeleton className="h-4 w-20" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-3 w-24" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : data ? (
          <>
            {/* 概览统计 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总访问量</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data.overview.totalVisits.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    页面浏览: {data.overview.pageViews.toLocaleString()}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">独立访客</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data.overview.uniqueVisitors.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    跳出率: {data.overview.bounceRate}%
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">搜索次数</CardTitle>
                  <Search className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data.overview.searchEvents.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    数据查询活动
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">活跃度</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round((data.overview.searchEvents / data.overview.totalVisits) * 100)}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    搜索转化率
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 详细统计 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* 热门数据库 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    热门数据库
                  </CardTitle>
                  <CardDescription>访问量最高的数据库</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.topDatabases.slice(0, 5).map((db, index) => (
                      <div key={db.name} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                            {index + 1}
                          </Badge>
                          <span className="text-sm font-medium">{db.name}</span>
                        </div>
                        <Badge variant="secondary">
                          {db.visits.toLocaleString()}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 热门页面 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MousePointer className="h-5 w-5" />
                    热门页面
                  </CardTitle>
                  <CardDescription>访问量最高的页面</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.topPages.slice(0, 5).map((page, index) => (
                      <div key={page.path} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                            {index + 1}
                          </Badge>
                          <span className="text-sm font-medium truncate max-w-48">
                            {page.path}
                          </span>
                        </div>
                        <Badge variant="secondary">
                          {page.visits.toLocaleString()}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 事件类型统计 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  用户行为事件
                </CardTitle>
                <CardDescription>不同类型的用户交互统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {data.eventTypes.map((event) => (
                    <div key={event.type} className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {event.count.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {event.type || '未知事件'}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 数据更新时间 */}
            <div className="mt-6 text-center text-sm text-gray-500">
              <Clock className="inline h-4 w-4 mr-1" />
              数据更新时间: {new Date(data.generatedAt).toLocaleString('zh-CN')}
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
}
