import { useState, useCallback, useEffect, useRef } from 'react';
import { debounce } from '@/lib/performance';

export interface GlobalSearchResult {
  database: string;
  count: number;
}

export function useGlobalSearch() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<GlobalSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const cacheRef = useRef<Record<string, GlobalSearchResult[]>>({});
  const abortRef = useRef<AbortController | null>(null);
  const debouncedSearchRef = useRef<ReturnType<typeof debounce>>();

  // 搜索函数
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setIsSearching(false);
      return;
    }

    // 检查缓存
    if (cacheRef.current[searchQuery]) {
      setResults(cacheRef.current[searchQuery]);
      setIsSearching(false);
      return;
    }

    try {
      // 取消之前的请求
      abortRef.current?.abort();
      const controller = new AbortController();
      abortRef.current = controller;

      const response = await fetch(`/api/global-search?q=${encodeURIComponent(searchQuery)}`, {
        signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        cacheRef.current[searchQuery] = data.data as GlobalSearchResult[];
        setResults(data.data as GlobalSearchResult[]);
      } else {
        console.error('Search API error:', data.error);
        setResults([]);
      }
    } catch (error) {
      if ((error as any)?.name !== 'AbortError') {
        console.error('Global search failed:', error);
        setResults([]);
      }
    } finally {
      setIsSearching(false);
    }
  }, []);

  // 创建防抖搜索函数
  useEffect(() => {
    if (!debouncedSearchRef.current) {
      debouncedSearchRef.current = debounce((searchQuery: string) => {
        performSearch(searchQuery);
      }, 300);
    }
  }, [performSearch]);

  // 处理查询变化
  const handleQueryChange = useCallback((newQuery: string) => {
    setQuery(newQuery);

    if (newQuery.trim().length >= 2) {
      setIsSearching(true);
      debouncedSearchRef.current?.(newQuery.trim());
    } else if (newQuery.trim().length === 0) {
      setResults([]);
      setIsSearching(false);
    }
  }, []);

  return {
    query,
    setQuery: handleQueryChange,
    results,
    isSearching
  };
}