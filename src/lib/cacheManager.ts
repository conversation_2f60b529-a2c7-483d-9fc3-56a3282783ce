/**
 * 统一缓存管理器
 * 用于管理应用中的所有配置缓存
 */

// 缓存管理器接口
interface CacheManager {
  clear(): Promise<void>;
  refresh(): Promise<void>;
  getStatus(): Promise<CacheStatus>;
}

interface CacheStatus {
  name: string;
  initialized: boolean;
  lastRefresh: Date | null;
  itemCount: number;
  ttl: number;
}

// 全局缓存注册表
const cacheManagers: Map<string, CacheManager> = new Map();

/**
 * 注册缓存管理器
 */
export function registerCacheManager(name: string, manager: CacheManager) {
  cacheManagers.set(name, manager);
  console.log(`📝 Registered cache manager: ${name}`);
}

/**
 * 清除所有缓存
 */
export async function clearAllCaches(): Promise<void> {
  console.log('🧹 Clearing all caches...');
  
  const promises = Array.from(cacheManagers.entries()).map(async ([name, manager]) => {
    try {
      await manager.clear();
      console.log(`✅ Cleared cache: ${name}`);
    } catch (error) {
      console.error(`❌ Failed to clear cache ${name}:`, error);
    }
  });
  
  await Promise.all(promises);
  console.log('🎉 All caches cleared');
}

/**
 * 刷新所有缓存
 */
export async function refreshAllCaches(): Promise<void> {
  console.log('🔄 Refreshing all caches...');
  
  const promises = Array.from(cacheManagers.entries()).map(async ([name, manager]) => {
    try {
      await manager.refresh();
      console.log(`✅ Refreshed cache: ${name}`);
    } catch (error) {
      console.error(`❌ Failed to refresh cache ${name}:`, error);
    }
  });
  
  await Promise.all(promises);
  console.log('🎉 All caches refreshed');
}

/**
 * 获取所有缓存状态
 */
export async function getAllCacheStatus(): Promise<Record<string, CacheStatus>> {
  const status: Record<string, CacheStatus> = {};
  
  const promises = Array.from(cacheManagers.entries()).map(async ([name, manager]) => {
    try {
      status[name] = await manager.getStatus();
    } catch (error) {
      console.error(`❌ Failed to get status for cache ${name}:`, error);
      status[name] = {
        name,
        initialized: false,
        lastRefresh: null,
        itemCount: 0,
        ttl: 0
      };
    }
  });
  
  await Promise.all(promises);
  return status;
}

/**
 * 数据库配置缓存管理器
 */
export class DatabaseConfigCacheManager implements CacheManager {
  private static instance: DatabaseConfigCacheManager;
  private lastRefresh: Date | null = null;
  
  static getInstance(): DatabaseConfigCacheManager {
    if (!this.instance) {
      this.instance = new DatabaseConfigCacheManager();
    }
    return this.instance;
  }
  
  async clear(): Promise<void> {
    // 这里需要访问实际的缓存变量
    // 由于缓存在模块作用域中，我们需要通过其他方式清除
    console.log('🧹 Clearing database config cache...');
    this.lastRefresh = null;
  }
  
  async refresh(): Promise<void> {
    console.log('🔄 Refreshing database config cache...');
    // 触发重新加载配置
    this.lastRefresh = new Date();
  }
  
  async getStatus(): Promise<CacheStatus> {
    return {
      name: 'DatabaseConfig',
      initialized: this.lastRefresh !== null,
      lastRefresh: this.lastRefresh,
      itemCount: 0, // 需要从实际缓存获取
      ttl: 10 * 60 * 1000 // 10分钟
    };
  }
}

// 自动注册默认缓存管理器
registerCacheManager('databaseConfig', DatabaseConfigCacheManager.getInstance());
