"use client";

import { ReactNode, useEffect, useState } from 'react';
import Link from "next/link";

interface SafeAccessCheckProps {
  database: string;
  children: ReactNode;
}

interface DatabaseConfig {
  name: string;
  accessLevel: string;
  category: string;
  description?: string;
}

/**
 * 安全的权限检查组件 - 避免复杂依赖
 */
export default function SafeAccessCheck({ database, children }: SafeAccessCheckProps) {
  const [accessLevel, setAccessLevel] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);

  // 获取用户信息
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const response = await fetch('/api/auth/me');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setUser(data.user);
          }
        }
      } catch (error) {
        console.error('Failed to fetch user:', error);
      }
    };

    fetchUser();
  }, []);

  // 获取数据库访问级别
  useEffect(() => {
    const fetchAccessLevel = async () => {
      try {
        setError(null);
        const response = await fetch('/api/config/databases');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data[database]) {
            const dbConfig = result.data[database];
            setAccessLevel(dbConfig.accessLevel || 'premium');
          } else {
            setAccessLevel('not_found');
          }
        } else {
          setAccessLevel('api_error');
        }
      } catch (error) {
        console.error('Failed to fetch database config:', error);
        setAccessLevel('api_error');
        setError('Failed to load database configuration');
      } finally {
        setLoading(false);
      }
    };

    fetchAccessLevel();
  }, [database]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">Checking permissions...</div>
      </div>
    );
  }

  // 数据库不存在
  if (accessLevel === 'not_found') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Database Not Found</h1>
          <p className="text-gray-600 mb-6">
            The database "{database}" does not exist or has been removed.
          </p>
          <div className="space-x-4">
            <Link href="/" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Back to Home
            </Link>
            <Link href="/data" className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              Browse Databases
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // API错误
  if (accessLevel === 'api_error') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Service Temporarily Unavailable</h1>
          <p className="text-gray-600 mb-6">
            Unable to verify database access. Please try again later.
          </p>
          {error && (
            <p className="text-red-600 text-sm mb-4">{error}</p>
          )}
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // 免费数据库：所有人都可以访问
  if (accessLevel === 'free') {
    return <>{children}</>;
  }

  // 需要高级权限的数据库
  if (accessLevel === 'premium' || accessLevel === 'enterprise') {
    // 未登录用户
    if (!user) {
      return (
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="text-yellow-600 mr-3">🔒</div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-yellow-800">Access Restricted</h3>
                <p className="text-yellow-700 mt-1">
                  This database requires {accessLevel} privileges. Please login or register to access.
                </p>
              </div>
              <div className="flex space-x-2">
                <Link 
                  href="/login"
                  className="px-4 py-2 border border-yellow-300 text-yellow-700 rounded hover:bg-yellow-100"
                >
                  Login
                </Link>
                <Link 
                  href="/register"
                  className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
                >
                  Register
                </Link>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // 检查会员级别
    const userLevel = user.membershipType;
    const hasAccess =
      (accessLevel === 'premium' && (userLevel === 'premium' || userLevel === 'enterprise')) ||
      (accessLevel === 'enterprise' && userLevel === 'enterprise');

    if (hasAccess) {
      return <>{children}</>;
    }

    // 权限不足
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="text-red-600 mr-3">🔒</div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-red-800">Insufficient Permissions</h3>
              <p className="text-red-700 mt-1">
                This database requires {accessLevel} privileges. Your current plan: {userLevel}
              </p>
            </div>
            <div>
              <Link 
                href="/upgrade"
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Upgrade Now
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 默认情况：允许访问
  return <>{children}</>;
}
